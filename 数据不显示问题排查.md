# 交易明细编辑页面数据不显示问题排查指南

## 问题描述
从库存信息编辑页面跳转到交易明细编辑页面时，编辑页面中没有显示数据。

## 排查步骤

### 1. 检查控制台日志
打开浏览器开发者工具，查看控制台输出，按以下顺序检查：

#### 主页面（edit.vue）日志：
```
编辑模式 - 传递的交易数据: {id: xxx, bizNo: xxx, ...}
页面跳转成功，准备发送数据
发送的数据: {transactionForm: {...}, isUpdate: true, stockId: xxx}
```

#### 编辑页面（transactionEdit.vue）日志：
```
交易明细编辑页面onLoad开始
获取事件通道: 成功
交易明细编辑页面接收到数据: {transactionForm: {...}, isUpdate: true, stockId: xxx}
合并前的表单数据: {id: undefined, bizId: undefined, ...}
合并后的表单数据: {id: xxx, bizId: xxx, ...}
初始化完成后的表单数据: {id: xxx, bizId: xxx, ...}
```

### 2. 常见问题及解决方案

#### 问题1: 没有看到"编辑模式 - 传递的交易数据"日志
**原因**: 传入的transaction对象为空或没有id/tempId
**解决**: 检查点击的交易明细数据是否完整

#### 问题2: 看到"页面跳转失败"日志
**原因**: 页面路径错误或页面不存在
**解决**: 检查页面路径是否正确

#### 问题3: 看到"获取事件通道: 失败"日志
**原因**: uni-app事件通道机制问题
**解决**: 
- 重启应用
- 清除缓存
- 检查uni-app版本

#### 问题4: 没有看到"交易明细编辑页面接收到数据"日志
**原因**: 事件通道数据传递失败
**解决**: 
- 检查事件名称是否匹配
- 确认数据发送时机

#### 问题5: 看到数据接收日志但表单仍为空
**原因**: Vue响应式更新问题或数据合并问题
**解决**: 
- 检查Object.assign是否正确执行
- 确认$forceUpdate是否被调用

### 3. 手动验证步骤

#### 步骤1: 验证数据传递
在主页面控制台执行：
```javascript
// 检查当前选中的交易数据
console.log('当前交易列表:', this.transactionList)
```

#### 步骤2: 验证页面状态
在编辑页面控制台执行：
```javascript
// 检查当前表单数据
console.log('表单数据:', this.transactionForm)
console.log('更新模式:', this.isUpdate)
console.log('库存ID:', this.stockId)
```

#### 步骤3: 手动设置测试数据
在编辑页面控制台执行：
```javascript
// 手动设置测试数据
this.transactionForm.bizId = 'TEST123'
this.transactionForm.bizNo = 'BIZ001'
this.transactionForm.quantity = 100
this.$forceUpdate()
```

### 4. 代码修复记录

#### 修复1: 添加调试日志
- 在数据传递的各个环节添加详细日志
- 便于跟踪数据流向和定位问题

#### 修复2: 改进数据合并逻辑
```javascript
// 原来的方式
this.transactionForm = { ...this.transactionForm, ...data.transactionForm }

// 改进的方式
Object.assign(this.transactionForm, data.transactionForm)
```

#### 修复3: 添加强制更新
```javascript
this.$nextTick(() => {
    this.$forceUpdate()
})
```

#### 修复4: 添加备用初始化
当事件通道获取失败时，提供备用的初始化机制

### 5. 预防措施

#### 数据完整性检查
在传递数据前检查：
```javascript
if(transaction && (transaction.id || transaction.tempId)){
    // 数据有效，可以传递
} else {
    console.error('交易数据无效:', transaction)
    return
}
```

#### 异步处理
确保数据初始化完成后再进行UI更新：
```javascript
this.initData().then(() => {
    this.$nextTick(() => {
        this.$forceUpdate()
    })
})
```

### 6. 测试验证

#### 测试用例1: 编辑现有交易
1. 在交易列表中点击编辑按钮
2. 检查编辑页面是否显示原有数据
3. 验证所有字段都正确填充

#### 测试用例2: 新增交易
1. 点击添加明细按钮
2. 检查编辑页面是否为空表单
3. 验证isUpdate为false

#### 测试用例3: 数据保存
1. 修改表单数据
2. 点击保存
3. 返回主页面检查数据是否更新

### 7. 联系支持

如果按照以上步骤仍无法解决问题，请提供：
1. 完整的控制台日志
2. 问题复现步骤
3. 使用的设备和浏览器信息
4. uni-app版本信息

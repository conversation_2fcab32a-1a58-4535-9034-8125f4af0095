# 库存交易明细数据同步测试用例

## 测试环境
- 页面路径: `pages/biz/scm/inventory/stockInfo/edit.vue`
- 相关页面: `pages/biz/scm/inventory/stockInfo/transactionEdit.vue`

## 测试用例1: 新增交易明细
### 测试步骤
1. 打开库存信息编辑页面
2. 点击"添加明细"按钮
3. 在交易明细编辑页面填写以下信息：
   - 交易类型: 选择任意类型
   - 交易方向: 选择任意方向
   - 数量: 输入100
   - 备注: 输入"测试新增明细"
4. 点击"保存"按钮
5. 等待页面返回到库存信息编辑页面

### 预期结果
- 新增的明细应该立即显示在交易明细列表中
- 明细信息应该与输入的信息一致
- 控制台应该显示相关的调试信息

## 测试用例2: 编辑现有交易明细
### 前置条件
- 库存信息中已存在至少一条交易明细

### 测试步骤
1. 打开库存信息编辑页面
2. 点击现有明细的"编辑"按钮
3. 在交易明细编辑页面修改以下信息：
   - 数量: 修改为200
   - 备注: 修改为"测试编辑明细"
4. 点击"保存"按钮
5. 等待页面返回到库存信息编辑页面

### 预期结果
- 修改的明细应该立即在列表中更新
- 显示的数量应该是200
- 备注应该显示"测试编辑明细"
- 其他未修改的字段应该保持不变

## 测试用例3: 删除交易明细
### 前置条件
- 库存信息中已存在至少一条交易明细

### 测试步骤
1. 打开库存信息编辑页面
2. 点击某条明细的"删除"按钮
3. 在确认对话框中点击"确定"

### 预期结果
- 该明细应该立即从列表中消失
- 显示"删除成功"的提示
- 其他明细不受影响

## 测试用例4: 多次编辑操作
### 测试步骤
1. 打开库存信息编辑页面
2. 新增一条明细（数量100）
3. 编辑刚新增的明细（数量改为150）
4. 再次编辑该明细（数量改为200）
5. 新增另一条明细（数量300）

### 预期结果
- 每次操作后，界面都应该立即更新
- 最终应该显示两条明细：数量200和数量300
- 所有修改都应该正确保存在内存中

## 测试用例5: 页面刷新测试
### 测试步骤
1. 完成测试用例4的操作
2. 点击"保存"按钮保存整个库存信息
3. 重新进入该库存信息的编辑页面

### 预期结果
- 页面重新加载后，应该显示之前保存的所有明细
- 数据应该与保存前一致

## 调试信息检查
在测试过程中，请注意控制台的以下调试信息：

### edit.vue中的调试信息
- "接收到交易明细保存事件:" - 确认事件正确接收
- "交易明细数据已更新:" - 确认数据更新成功
- "交易明细数据加载完成:" - 确认初始数据加载

### transactionEdit.vue中的调试信息
- "发送交易明细保存事件:" - 确认事件正确发送
- "无法获取事件通道" - 如果出现此信息，说明事件通道有问题

## 常见问题排查

### 问题1: TypeError: Cannot read properties of undefined
**错误信息**: `Cannot read properties of undefined (reading 'tempId||index')`
**可能原因**:
- Vue列表渲染的key值计算出错
- 数组中包含undefined或null项

**排查方法**:
- 检查控制台是否有此类型错误
- 确认transactionList数组中的数据完整性
- 验证getTransactionKey方法是否正常工作

**修复状态**: 已修复，添加了防护性检查和专用的key生成方法

### 问题2: 修改后数据没有显示
**可能原因**:
- 事件通道没有正确建立
- Vue响应式更新失败

**排查方法**:
- 检查控制台是否有"发送交易明细保存事件"和"接收到交易明细保存事件"
- 检查是否有JavaScript错误

### 问题2: 数据显示但保存后丢失
**可能原因**:
- 数据没有正确包含在保存请求中
- 服务器端处理有问题

**排查方法**:
- 检查保存时的请求数据是否包含transactionList
- 检查服务器响应

### 问题3: 页面卡顿或无响应
**可能原因**:
- 频繁的$forceUpdate()调用
- 数据量过大

**排查方法**:
- 检查控制台性能警告
- 减少测试数据量

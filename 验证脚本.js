// 库存交易明细数据同步修复验证脚本
// 此脚本用于在浏览器控制台中验证修复是否有效

console.log('=== 库存交易明细数据同步修复验证 ===');

// 模拟getTransactionKey方法
function getTransactionKey(transaction, index) {
    if (!transaction) {
        return 'empty-' + index;
    }
    if (transaction.id) {
        return 'id-' + transaction.id;
    }
    if (transaction.tempId) {
        return 'temp-' + transaction.tempId;
    }
    return 'index-' + index;
}

// 测试用例1: 正常数据
console.log('测试用例1: 正常数据');
const normalTransaction = { id: 123, tempId: 'temp123', bizNo: 'BIZ001' };
console.log('输入:', normalTransaction);
console.log('输出:', getTransactionKey(normalTransaction, 0));
console.log('预期: id-123');

// 测试用例2: 只有tempId的数据
console.log('\n测试用例2: 只有tempId的数据');
const tempTransaction = { tempId: 'temp456', bizNo: 'BIZ002' };
console.log('输入:', tempTransaction);
console.log('输出:', getTransactionKey(tempTransaction, 1));
console.log('预期: temp-temp456');

// 测试用例3: 空数据
console.log('\n测试用例3: 空数据');
const emptyTransaction = null;
console.log('输入:', emptyTransaction);
console.log('输出:', getTransactionKey(emptyTransaction, 2));
console.log('预期: empty-2');

// 测试用例4: undefined数据
console.log('\n测试用例4: undefined数据');
const undefinedTransaction = undefined;
console.log('输入:', undefinedTransaction);
console.log('输出:', getTransactionKey(undefinedTransaction, 3));
console.log('预期: empty-3');

// 测试用例5: 空对象
console.log('\n测试用例5: 空对象');
const emptyObjectTransaction = {};
console.log('输入:', emptyObjectTransaction);
console.log('输出:', getTransactionKey(emptyObjectTransaction, 4));
console.log('预期: index-4');

// 模拟数组过滤测试
console.log('\n=== 数组过滤测试 ===');
const testArray = [
    { id: 1, bizNo: 'BIZ001' },
    null,
    { id: 2, bizNo: 'BIZ002' },
    undefined,
    { tempId: 'temp123', bizNo: 'BIZ003' },
    null
];

console.log('原始数组:', testArray);
const filteredArray = testArray.filter(item => item != null);
console.log('过滤后数组:', filteredArray);
console.log('预期长度: 3');

// 模拟findIndex测试
console.log('\n=== findIndex测试 ===');
const searchId = 2;
const foundIndex = filteredArray.findIndex(item => {
    if (!item) {
        return false;
    }
    return item.id === searchId;
});
console.log('查找ID:', searchId);
console.log('找到索引:', foundIndex);
console.log('预期索引: 1');

// 验证Vue key生成的唯一性
console.log('\n=== Vue key唯一性测试 ===');
const mixedArray = [
    { id: 1, bizNo: 'BIZ001' },
    { tempId: 'temp123', bizNo: 'BIZ002' },
    null,
    { id: 2, bizNo: 'BIZ003' },
    undefined
];

const keys = mixedArray.map((item, index) => getTransactionKey(item, index));
console.log('生成的keys:', keys);
console.log('keys是否唯一:', new Set(keys).size === keys.length);

console.log('\n=== 验证完成 ===');
console.log('如果所有测试都通过，说明修复有效');

// 在实际页面中的验证方法
console.log('\n=== 实际页面验证方法 ===');
console.log('1. 打开库存信息编辑页面');
console.log('2. 在控制台中运行: this.getTransactionKey(null, 0)');
console.log('3. 应该返回: "empty-0"');
console.log('4. 检查是否还有TypeError错误');
console.log('5. 验证交易明细列表是否正常显示');
